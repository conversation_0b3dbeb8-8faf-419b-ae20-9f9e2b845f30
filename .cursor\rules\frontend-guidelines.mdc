---
description: 
globs: 
alwaysApply: false
---
## React 19 and Next.js 15 Best Practices

### Component Architecture

-   Favor React Server Components (RSC) where possible
-   Minimize 'use client' directives
-   Implement proper error boundaries
-   Use Suspense for async operations
-   Optimize for performance and Web Vitals
-   Integrate SEO best practices and metadata management.

### State Management

-   Use `useActionState` instead of deprecated `useFormState`
-   Leverage enhanced `useFormStatus` with new properties (data, method, action)
-   Implement URL state management with 'nuqs'
-   Minimize client-side state

### UI/UX

-   Use Shadcn UI components for consistent design
-   Implement responsive design with Tailwind CSS
-   Use Radix UI for accessibility and focus management
-   Follow accessibility standards and guidelines

### i18n

-   Use Next.js 15's built-in internationalization features
-   Implement server-side rendering (SSR) for better SEO
-   Use `next-intl` for client-side internationalization
-   Follow best practices for i18n configuration

### PWA

-   Implement a service worker for offline functionality
-   Use `Serwist` for PWA configuration
-   Follow best practices for PWA implementation

### Async Request APIs

```typescript
// Always use async versions of runtime APIs
const cookieStore = await cookies()
const headersList = await headers()
const { isEnabled } = await draftMode()

// Handle async params in layouts/pages
const params = await props.params
const searchParams = await props.searchParams
```
