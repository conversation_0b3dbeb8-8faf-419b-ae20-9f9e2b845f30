{"name": "nextjs-web3-starter", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint:lint-staged": "lint-staged -c ./.husky/lintstagedrc.js", "lint:pretty": "pretty-quick --staged", "lint": "pnpm lint:lint-staged && pnpm lint:pretty", "prepare": "husky", "log": "npx conventional-changelog --config ./node_modules/@commitlint/cli -i CHANGELOG.md -s -r 0"}, "dependencies": {"@hookform/resolvers": "^5.2.1", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@serwist/next": "^9.0.15", "@types/react-pdf": "^7.0.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "html2canvas": "^1.4.1", "jspdf": "^3.0.1", "lucide-react": "^0.525.0", "next": "^15.4.1", "next-intl": "^4.3.4", "next-themes": "^0.4.6", "puppeteer": "^24.15.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hook-form": "^7.62.0", "serwist": "^9.0.15", "sonner": "^2.0.6", "swr": "^2.3.4", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "vaul": "^1.1.2", "zod": "^4.0.14", "zustand": "^5.0.6"}, "devDependencies": {"@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "@eslint/eslintrc": "^3.3.1", "@tailwindcss/postcss": "^4.1.11", "@types/node": "^24", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "conventional-changelog-cli": "^5.0.0", "eslint": "^9.31.0", "eslint-config-next": "^15.4.1", "eslint-config-prettier": "^10.1.5", "husky": "^9.1.7", "lint-staged": "^16.1.2", "prettier": "^3.6.2", "prettier-plugin-tailwindcss": "^0.6.14", "pretty-quick": "^4.2.2", "tailwindcss": "^4.1.11", "typescript": "^5.8.3"}}