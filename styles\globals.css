@import 'tailwindcss';

@plugin "tailwindcss-animate";

@custom-variant dark (&:is(.dark *));

@theme {
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

:root {
  --background: oklch(1 0 0);
  --foreground: oklch(0.141 0.005 285.823);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.141 0.005 285.823);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.141 0.005 285.823);
  --primary: oklch(0.21 0.006 285.885);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.967 0.001 286.375);
  --secondary-foreground: oklch(0.21 0.006 285.885);
  --muted: oklch(0.967 0.001 286.375);
  --muted-foreground: oklch(0.552 0.016 285.938);
  --accent: oklch(0.967 0.001 286.375);
  --accent-foreground: oklch(0.21 0.006 285.885);
  --destructive: oklch(0.577 0.245 27.325);
  --destructive-foreground: oklch(0.577 0.245 27.325);
  --border: oklch(0.92 0.004 286.32);
  --input: oklch(0.92 0.004 286.32);
  --ring: oklch(0.705 0.015 286.067);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --radius: 0.625rem;
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.141 0.005 285.823);
  --sidebar-primary: oklch(0.21 0.006 285.885);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.967 0.001 286.375);
  --sidebar-accent-foreground: oklch(0.21 0.006 285.885);
  --sidebar-border: oklch(0.92 0.004 286.32);
  --sidebar-ring: oklch(0.705 0.015 286.067);
}

.dark {
  --background: oklch(0.141 0.005 285.823);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.141 0.005 285.823);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.141 0.005 285.823);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.985 0 0);
  --primary-foreground: oklch(0.21 0.006 285.885);
  --secondary: oklch(0.274 0.006 286.033);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.274 0.006 286.033);
  --muted-foreground: oklch(0.705 0.015 286.067);
  --accent: oklch(0.274 0.006 286.033);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.396 0.141 25.723);
  --destructive-foreground: oklch(0.637 0.237 25.331);
  --border: oklch(0.274 0.006 286.033);
  --input: oklch(0.274 0.006 286.033);
  --ring: oklch(0.442 0.017 285.786);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.21 0.006 285.885);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.274 0.006 286.033);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(0.274 0.006 286.033);
  --sidebar-ring: oklch(0.442 0.017 285.786);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* 主题切换过渡动画 */
.theme-transition * {
  transition:
    background-color 0.3s ease-in-out,
    color 0.3s ease-in-out,
    border-color 0.3s ease-in-out;
}

/* 可选：添加淡入淡出动画 */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

html {
  animation: fadeIn 0.3s ease-in-out;
}

/* ============ iOS26风格液态玻璃效果 ============ */

/* Header液态玻璃效果 */
.liquid-glass-header {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0)), linear-gradient(rgba(255, 255, 255, 0.05), rgba(255, 255, 255, 0.02));
  backdrop-filter: blur(20px) saturate(180%);
  -webkit-backdrop-filter: blur(20px) saturate(180%);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  position: relative;
  z-index: 50;
}

.liquid-glass-header-scrolled {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.05)), linear-gradient(rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
  backdrop-filter: blur(40px) saturate(200%);
  -webkit-backdrop-filter: blur(40px) saturate(200%);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.2),
    inset 0 -1px 0 rgba(255, 255, 255, 0.1);
}

.dark .liquid-glass-header {
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.1), rgba(0, 0, 0, 0)), linear-gradient(rgba(255, 255, 255, 0.03), rgba(255, 255, 255, 0.01));
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.dark .liquid-glass-header-scrolled {
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.2), rgba(0, 0, 0, 0.05)), linear-gradient(rgba(255, 255, 255, 0.05), rgba(255, 255, 255, 0.02));
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.1),
    inset 0 -1px 0 rgba(255, 255, 255, 0.05);
}

/* 液态玻璃容器 */
.liquid-glass-container {
  position: relative;
  background: radial-gradient(ellipse at top, rgba(255, 255, 255, 0.1), transparent 70%), linear-gradient(to bottom, rgba(255, 255, 255, 0.05), rgba(255, 255, 255, 0.02));
  border-radius: 0;
  overflow: hidden;
}

.liquid-glass-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
}

.dark .liquid-glass-container {
  background: radial-gradient(ellipse at top, rgba(255, 255, 255, 0.05), transparent 70%), linear-gradient(to bottom, rgba(255, 255, 255, 0.02), rgba(255, 255, 255, 0.01));
}

/* 小型液态玻璃容器 */
.liquid-glass-container-small {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.1)), linear-gradient(rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
  backdrop-filter: blur(15px) saturate(180%);
  -webkit-backdrop-filter: blur(15px) saturate(180%);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  box-shadow:
    0 4px 16px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.dark .liquid-glass-container-small {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05)), linear-gradient(rgba(255, 255, 255, 0.05), rgba(255, 255, 255, 0.02));
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow:
    0 4px 16px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

/* 液态玻璃按钮 */
.liquid-glass-button {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.25), rgba(255, 255, 255, 0.1)), linear-gradient(rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0.05));
  backdrop-filter: blur(15px) saturate(180%);
  -webkit-backdrop-filter: blur(15px) saturate(180%);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  box-shadow:
    0 4px 16px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.liquid-glass-button:hover {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.4), rgba(255, 255, 255, 0.2)), linear-gradient(rgba(255, 255, 255, 0.25), rgba(255, 255, 255, 0.1));
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.4);
  transform: translateY(-1px);
}

.dark .liquid-glass-button {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0.05)), linear-gradient(rgba(255, 255, 255, 0.08), rgba(255, 255, 255, 0.02));
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow:
    0 4px 16px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.15);
}

.dark .liquid-glass-button:hover {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.25), rgba(255, 255, 255, 0.1)), linear-gradient(rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0.05));
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.25);
}

/* 主要按钮液态玻璃效果 */
.liquid-glass-button-primary {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.9), rgba(147, 51, 234, 0.8)), linear-gradient(rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.1));
  backdrop-filter: blur(15px) saturate(180%);
  -webkit-backdrop-filter: blur(15px) saturate(180%);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 16px;
  box-shadow:
    0 8px 32px rgba(59, 130, 246, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.4),
    0 0 20px rgba(59, 130, 246, 0.2);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  color: white;
  font-weight: 600;
  position: relative;
  overflow: hidden;
}

.liquid-glass-button-primary:hover {
  background: linear-gradient(135deg, rgba(59, 130, 246, 1), rgba(147, 51, 234, 0.9)), linear-gradient(rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.2));
  box-shadow:
    0 12px 48px rgba(59, 130, 246, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.5),
    0 0 30px rgba(59, 130, 246, 0.3);
  transform: translateY(-2px) scale(1.02);
}

/* 次要按钮液态玻璃效果 */
.liquid-glass-button-secondary {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.1)), linear-gradient(rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.05));
  backdrop-filter: blur(15px) saturate(180%);
  -webkit-backdrop-filter: blur(15px) saturate(180%);
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 16px;
  box-shadow:
    0 4px 16px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.4);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.liquid-glass-button-secondary:hover {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.5), rgba(255, 255, 255, 0.2)), linear-gradient(rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.1));
  border: 2px solid rgba(255, 255, 255, 0.5);
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.5);
  transform: translateY(-1px) scale(1.02);
}

.dark .liquid-glass-button-secondary {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0.05)), linear-gradient(rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.02));
  border: 2px solid rgba(255, 255, 255, 0.15);
}

.dark .liquid-glass-button-secondary:hover {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.25), rgba(255, 255, 255, 0.1)), linear-gradient(rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.05));
  border: 2px solid rgba(255, 255, 255, 0.3);
}

/* 导航项液态效果 */
.liquid-nav-item {
  position: relative;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.liquid-nav-hover:hover {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.1)), linear-gradient(rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.liquid-nav-active {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.2), rgba(147, 51, 234, 0.1)), linear-gradient(rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0.05));
  backdrop-filter: blur(15px);
  -webkit-backdrop-filter: blur(15px);
  box-shadow:
    inset 0 1px 0 rgba(255, 255, 255, 0.3),
    0 0 20px rgba(59, 130, 246, 0.1);
}

/* 液态指示器 */
.liquid-indicator {
  background: linear-gradient(90deg, rgba(59, 130, 246, 0.8), rgba(147, 51, 234, 0.8), rgba(236, 72, 153, 0.8));
  box-shadow:
    0 0 8px rgba(59, 130, 246, 0.4),
    0 0 16px rgba(147, 51, 234, 0.3);
  animation: liquid-flow 4s ease-in-out infinite;
  position: relative;
}

.liquid-indicator::before {
  content: '';
  position: absolute;
  inset: 0;
  background: inherit;
  border-radius: inherit;
  filter: blur(4px);
  opacity: 0.6;
  z-index: -1;
}

@keyframes liquid-flow {
  0%,
  100% {
    transform: translateX(-50%) scaleX(1);
    filter: hue-rotate(0deg);
  }
  25% {
    transform: translateX(-50%) scaleX(1.1);
    filter: hue-rotate(15deg);
  }
  50% {
    transform: translateX(-50%) scaleX(1.3);
    filter: hue-rotate(30deg);
  }
  75% {
    transform: translateX(-50%) scaleX(1.1);
    filter: hue-rotate(15deg);
  }
}

/* 液态玻璃卡片 */
.liquid-glass-card {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.25), rgba(255, 255, 255, 0.1)), linear-gradient(rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0.05));
  backdrop-filter: blur(20px) saturate(180%);
  -webkit-backdrop-filter: blur(20px) saturate(180%);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.3),
    inset 0 -1px 0 rgba(255, 255, 255, 0.1);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.liquid-glass-card:hover {
  border: 1px solid rgba(255, 255, 255, 0.4);
  box-shadow:
    0 16px 64px rgba(0, 0, 0, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.4),
    inset 0 -1px 0 rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
}

.dark .liquid-glass-card {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05)), linear-gradient(rgba(255, 255, 255, 0.08), rgba(255, 255, 255, 0.02));
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.15);
}

.dark .liquid-glass-card:hover {
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow:
    0 16px 64px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.25);
}

/* 液态玻璃徽章 */
.liquid-glass-badge {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.1)), linear-gradient(rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.05));
  backdrop-filter: blur(15px) saturate(180%);
  -webkit-backdrop-filter: blur(15px) saturate(180%);
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow:
    0 4px 16px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.4);
}

.dark .liquid-glass-badge {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0.05)), linear-gradient(rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.02));
  border: 1px solid rgba(255, 255, 255, 0.15);
}

/* 移动端菜单液态玻璃 */
.liquid-glass-mobile-menu {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.8)), linear-gradient(rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
  backdrop-filter: blur(40px) saturate(200%);
  -webkit-backdrop-filter: blur(40px) saturate(200%);
  border-bottom: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow:
    0 16px 64px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.5);
}

.dark .liquid-glass-mobile-menu {
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.9), rgba(0, 0, 0, 0.8)), linear-gradient(rgba(255, 255, 255, 0.05), rgba(255, 255, 255, 0.02));
  border-bottom: 1px solid rgba(255, 255, 255, 0.15);
  box-shadow:
    0 16px 64px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

/* 下拉菜单液态玻璃 */
.liquid-glass-dropdown {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.8)), linear-gradient(rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
  backdrop-filter: blur(40px) saturate(200%);
  -webkit-backdrop-filter: blur(40px) saturate(200%);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 16px;
  box-shadow:
    0 16px 64px rgba(0, 0, 0, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.5);
}

.dark .liquid-glass-dropdown {
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.9), rgba(0, 0, 0, 0.8)), linear-gradient(rgba(255, 255, 255, 0.05), rgba(255, 255, 255, 0.02));
  border: 1px solid rgba(255, 255, 255, 0.15);
  box-shadow:
    0 16px 64px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

/* 科技风增强样式 */

/* 玻璃模糊效果增强 */
.glass-morphism {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
}

.dark .glass-morphism {
  background: rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.37);
}

/* 液态发光效果 */
.liquid-glow {
  position: relative;
}

.liquid-glow::before {
  content: '';
  position: absolute;
  inset: 0;
  border-radius: inherit;
  background: inherit;
  filter: blur(8px);
  opacity: 0.6;
  z-index: -1;
  transition: all 0.3s ease;
}

.liquid-glow:hover::before {
  filter: blur(12px);
  opacity: 0.8;
}

/* 霓虹发光效果 */
.neon-glow {
  box-shadow:
    0 0 5px theme('colors.blue.500'),
    0 0 10px theme('colors.blue.500'),
    0 0 15px theme('colors.blue.500'),
    0 0 20px theme('colors.blue.500');
}

.dark .neon-glow {
  box-shadow:
    0 0 5px theme('colors.blue.400'),
    0 0 10px theme('colors.blue.400'),
    0 0 15px theme('colors.blue.400'),
    0 0 20px theme('colors.blue.400');
}

/* 渐变边框 */
.gradient-border {
  background: linear-gradient(45deg, theme('colors.blue.500'), theme('colors.purple.500'));
  padding: 1px;
  border-radius: 12px;
}

.gradient-border-content {
  background: theme('colors.white');
  border-radius: 11px;
  height: 100%;
  width: 100%;
}

.dark .gradient-border-content {
  background: theme('colors.gray.900');
}

/* 动态背景网格 */
.grid-background {
  background-image: linear-gradient(rgba(0, 0, 0, 0.1) 1px, transparent 1px), linear-gradient(90deg, rgba(0, 0, 0, 0.1) 1px, transparent 1px);
  background-size: 2rem 2rem;
  mask-image: radial-gradient(ellipse 80% 50% at 50% 0%, #000 70%, transparent 110%);
}

.dark .grid-background {
  background-image: linear-gradient(rgba(255, 255, 255, 0.05) 1px, transparent 1px), linear-gradient(90deg, rgba(255, 255, 255, 0.05) 1px, transparent 1px);
}

/* 悬浮动画 */
@keyframes float {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

.float-animation {
  animation: float 6s ease-in-out infinite;
}

/* 脉冲发光动画 */
@keyframes pulse-glow {
  0%,
  100% {
    box-shadow: 0 0 5px theme('colors.blue.500/50');
  }
  50% {
    box-shadow:
      0 0 20px theme('colors.blue.500/80'),
      0 0 30px theme('colors.blue.500/60');
  }
}

.pulse-glow {
  animation: pulse-glow 2s ease-in-out infinite;
}

/* 渐变文本动画 */
@keyframes gradient-shift {
  0%,
  100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

.gradient-text-animated {
  background: linear-gradient(-45deg, theme('colors.blue.600'), theme('colors.purple.600'), theme('colors.pink.600'), theme('colors.blue.600'));
  background-size: 400% 400%;
  animation: gradient-shift 3s ease infinite;
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(45deg, theme('colors.blue.500'), theme('colors.purple.500'));
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(45deg, theme('colors.blue.600'), theme('colors.purple.600'));
}

.dark ::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
}

/* 响应式优化 */
@media (max-width: 768px) {
  .liquid-glass-card,
  .liquid-glass-button,
  .liquid-glass-container-small {
    backdrop-filter: blur(15px);
    -webkit-backdrop-filter: blur(15px);
  }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  .liquid-glass-card,
  .liquid-glass-button,
  .liquid-glass-container-small {
    background: rgba(255, 255, 255, 0.9);
    border: 2px solid rgba(0, 0, 0, 0.8);
  }

  .dark .liquid-glass-card,
  .dark .liquid-glass-button,
  .dark .liquid-glass-container-small {
    background: rgba(0, 0, 0, 0.9);
    border: 2px solid rgba(255, 255, 255, 0.8);
  }
}

/* 减少动画设置支持 */
@media (prefers-reduced-motion: reduce) {
  .float-animation,
  .pulse-glow,
  .gradient-text-animated,
  .liquid-indicator {
    animation: none;
  }

  .theme-transition * {
    transition: none;
  }

  .liquid-glass-button,
  .liquid-glass-card,
  .liquid-nav-item {
    transition: none;
  }
}

/* Resume specific styles */
@layer components {
  .resume-preview {
    @apply bg-white text-gray-900 font-sans;
    font-family: 'PingFang SC', 'Microsoft YaHei', 'Helvetica Neue', Arial, sans-serif;
    line-height: 1.6;
  }

  .resume-preview h1 {
    @apply text-3xl font-bold text-gray-900 mb-2;
  }

  .resume-preview h2 {
    @apply text-xl font-bold text-gray-900 border-b border-gray-300 pb-2 mb-4;
  }

  .resume-preview h3 {
    @apply text-lg font-semibold text-gray-900;
  }

  .resume-preview .contact-info {
    @apply grid grid-cols-2 gap-4 text-sm text-gray-600;
  }

  .resume-preview .section {
    @apply mb-6;
  }

  .resume-preview .experience-item,
  .resume-preview .education-item,
  .resume-preview .project-item {
    @apply border-l-2 border-blue-200 pl-4 mb-4 relative;
  }

  .resume-preview .experience-item::before,
  .resume-preview .education-item::before,
  .resume-preview .project-item::before {
    content: '';
    @apply absolute -left-1 top-1 w-2 h-2 bg-blue-500 rounded-full;
  }

  .resume-preview .item-header {
    @apply flex justify-between items-start mb-2;
  }

  .resume-preview .item-title {
    @apply font-semibold text-gray-900;
  }

  .resume-preview .item-company {
    @apply text-blue-600 font-medium;
  }

  .resume-preview .item-date {
    @apply text-sm text-gray-600 text-right;
  }

  .resume-preview .item-description {
    @apply text-gray-700 text-sm leading-relaxed;
  }

  .resume-preview .skills-grid {
    @apply grid grid-cols-1 md:grid-cols-2 gap-4;
  }

  .resume-preview .skill-category {
    @apply mb-4;
  }

  .resume-preview .skill-category-title {
    @apply font-semibold text-gray-900 mb-2 flex items-center gap-2;
  }

  .resume-preview .skill-tags {
    @apply flex flex-wrap gap-2;
  }

  .resume-preview .skill-tag,
  .resume-preview .tech-tag {
    @apply bg-gray-100 text-gray-700 px-2 py-1 rounded-full text-xs;
  }

  .resume-preview .two-column {
    @apply grid grid-cols-1 md:grid-cols-2 gap-6;
  }

  .resume-preview .cert-item,
  .resume-preview .lang-item {
    @apply mb-3 text-sm;
  }

  .resume-preview .cert-name,
  .resume-preview .lang-name {
    @apply font-semibold text-gray-900;
  }

  .resume-preview .cert-issuer {
    @apply text-gray-600;
  }

  .resume-preview .cert-date {
    @apply text-gray-500 text-xs;
  }

  .resume-preview .lang-item {
    @apply flex justify-between items-center;
  }

  .resume-preview .lang-proficiency {
    @apply bg-gray-100 text-gray-600 px-2 py-1 rounded-full text-xs;
  }
}
