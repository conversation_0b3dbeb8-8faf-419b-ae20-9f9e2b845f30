---
description: 
globs: 
alwaysApply: false
---
## Code Style and Structure

### General Principles

-   Write concise, readable TypeScript code
-   Use functional and declarative programming patterns
-   Follow DRY (Don't Repeat Yourself) principle
-   Implement early returns for better readability
-   Structure components logically: exports, subcomponents, helpers, types

### Naming Conventions

-   Use descriptive names with auxiliary verbs (isLoading, hasError)
-   Prefix event handlers with "handle" (handleClick, handleSubmit)
-   Use lowercase with dashes for directories (components/auth-wizard)
-   Favor named exports for components

### TypeScript Usage

-   Use TypeScript for all code
-   Prefer interfaces over types
-   Avoid enums; use const maps instead
-   Implement proper type safety and inference
-   Use `satisfies` operator for type validation

