import type { Metadata } from 'next';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mon<PERSON> } from 'next/font/google';
import '~/styles/globals.css';
import { Toaster } from '~/components/ui/sonner';
import { ThemeProvider } from '~/components/provider/themeProvider';
import { Locale, routing } from '~/lib/i18n/routing';
import { getMessages, setRequestLocale } from 'next-intl/server';
import { NextIntlClientProvider } from 'next-intl';
import { notFound } from 'next/navigation';
import { SWRProvider } from '~/components/provider/swrProvider';

const geistSans = Geist({
  variable: '--font-geist-sans',
  subsets: ['latin'],
  display: 'swap', // ensure font display optimization
});

const geistMono = Geist_Mono({
  variable: '--font-geist-mono',
  subsets: ['latin'],
  display: 'swap', // ensure font display optimization
});

// extract shared configuration as constant
const THEME_COLORS = [
  { media: '(prefers-color-scheme: light)', color: 'white' },
  { media: '(prefers-color-scheme: dark)', color: '#141414' },
];

export const metadata: Metadata = {
  title: {
    default: 'Your App Name',
    template: '%s | Your App Name',
  },
  description: 'Generated by create next app',
  viewport: {
    width: 'device-width',
    initialScale: 1,
    maximumScale: 1,
    userScalable: false,
  },
  themeColor: THEME_COLORS,
};

// only use generateViewport when you need to dynamically generate based on parameters
// if the configuration is exactly the same, you can consider deleting this function
export function generateViewport() {
  // if you don't need to dynamically generate different configurations based on locale, you can delete this function
  // or add specific locale logic here
  return {
    // use constant to avoid repetition
    themeColor: THEME_COLORS,
  };
}

export default async function LocaleLayout({
  children,
  params,
}: Readonly<{
  children: React.ReactNode;
  params: Promise<{ locale: string }>;
}>) {
  const { locale } = await params;
  if (!routing.locales.includes(locale as Locale)) {
    notFound();
  }

  // Enable static rendering
  setRequestLocale(locale);

  const messages = await getMessages();
  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        {/* prevent flashing */}
        <script
          dangerouslySetInnerHTML={{
            __html: `
              try {
                (function() {
                  var savedTheme = localStorage.getItem('theme')
                  var systemDark = window.matchMedia('(prefers-color-scheme: dark)').matches
                  var theme = savedTheme || 'system'
                  var resolved = theme === 'system' ? (systemDark ? 'dark' : 'light') : theme
                  document.documentElement.classList.add(resolved)
                  document.documentElement.style.colorScheme = resolved
                })()
              } catch(e) { console.error(e) }
            `,
          }}
        />
      </head>
      <body className={`${geistSans.variable} ${geistMono.variable} antialiased`}>
        <NextIntlClientProvider messages={messages}>
          <ThemeProvider>
            <SWRProvider>
              {children}
              <Toaster />
            </SWRProvider>
          </ThemeProvider>
        </NextIntlClientProvider>
      </body>
    </html>
  );
}
