{"search.exclude": {"**/.yarn": true, "**/.pnp.*": true}, "editor.formatOnSave": true, "editor.quickSuggestions": {"other": true, "comments": true, "strings": true}, "files.associations": {"editor.snippetSuggestions": "top"}, "files.autoSave": "after<PERSON>elay", "[javascript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[typescript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[json]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[jsonc]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[markdown]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[toml]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[css]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "editor.tabSize": 2, "editor.insertSpaces": true, "editor.detectIndentation": false, "editor.formatOnSaveMode": "modifications", "i18n-ally.localesPaths": ["locales", "lib/i18n"]}