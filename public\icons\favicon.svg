<svg xmlns="http://www.w3.org/2000/svg" version="1.1" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:svgjs="http://svgjs.dev/svgjs" width="512" height="512"><svg version="1.0" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="512" height="512" viewBox="0 0 512 512">
    <svg id="SvgjsSvg1043" version="1.0" xmlns="http://www.w3.org/2000/svg" width="0" height="0" viewBox="0 0 340.000000 250.000000" preserveAspectRatio="xMidYMid meet" color-interpolation-filters="sRGB" class="el-tooltip" style="margin: auto;" xmlns:xlink="http://www.w3.org/1999/xlink">
    <defs>
      <pattern id="SvgjsPattern1042" patternUnits="userSpaceOnUse" width="100%" height="100%">
        <image xlink:href="https://www.logosc.cn/img/watermark/LOGOSC-watermark.png" x="0" y="0" width="100%" height="100%">
        </image>
      </pattern>
                    <linearGradient id="SvgjsLinearGradient1041" x1="0%" y1="0%" x2="100%" y2="0%">
          <stop offset="0%" stop-color="#fa71cd"></stop>
          <stop offset="100%" stop-color="#9b59b6"></stop>
        </linearGradient>
              <linearGradient id="SvgjsLinearGradient1040" x1="0%" y1="0%" x2="100%" y2="0%">
          <stop offset="0%" stop-color="#f9d423"></stop>
          <stop offset="100%" stop-color="#f83600"></stop>
        </linearGradient>
              <linearGradient id="SvgjsLinearGradient1039" x1="0%" y1="0%" x2="100%" y2="0%">
          <stop offset="0%" stop-color="#0064d2"></stop>
          <stop offset="100%" stop-color="#1cb0f6"></stop>
        </linearGradient>
              <linearGradient id="SvgjsLinearGradient1038" x1="0%" y1="0%" x2="100%" y2="0%">
          <stop offset="0%" stop-color="#f00978"></stop>
          <stop offset="100%" stop-color="#3f51b1"></stop>
        </linearGradient>
              <linearGradient id="SvgjsLinearGradient1037" x1="0%" y1="0%" x2="100%" y2="0%">
          <stop offset="0%" stop-color="#7873f5"></stop>
          <stop offset="100%" stop-color="#ec77ab"></stop>
        </linearGradient>
              <linearGradient id="SvgjsLinearGradient1036" x1="0%" y1="0%" x2="100%" y2="0%">
          <stop offset="0%" stop-color="#f9d423"></stop>
          <stop offset="100%" stop-color="#e14fad"></stop>
        </linearGradient>
              <linearGradient id="SvgjsLinearGradient1035" x1="0%" y1="0%" x2="100%" y2="0%">
          <stop offset="0%" stop-color="#009efd"></stop>
          <stop offset="100%" stop-color="#2af598"></stop>
        </linearGradient>
              <linearGradient id="SvgjsLinearGradient1034" x1="0%" y1="0%" x2="100%" y2="0%">
          <stop offset="0%" stop-color="#ffcc00"></stop>
          <stop offset="100%" stop-color="#00b140"></stop>
        </linearGradient>
              <linearGradient id="SvgjsLinearGradient1033" x1="0%" y1="0%" x2="100%" y2="0%">
          <stop offset="0%" stop-color="#d51007"></stop>
          <stop offset="100%" stop-color="#ff8177"></stop>
        </linearGradient>
              <linearGradient id="SvgjsLinearGradient1032" x1="0%" y1="0%" x2="100%" y2="0%">
          <stop offset="0%" stop-color="#a2b6df"></stop>
          <stop offset="100%" stop-color="#0c3483"></stop>
        </linearGradient>
              <linearGradient id="SvgjsLinearGradient1031" x1="0%" y1="0%" x2="100%" y2="0%">
          <stop offset="0%" stop-color="#7ac5d8"></stop>
          <stop offset="100%" stop-color="#eea2a2"></stop>
        </linearGradient>
              <linearGradient id="SvgjsLinearGradient1030" x1="0%" y1="0%" x2="100%" y2="0%">
          <stop offset="0%" stop-color="#00ecbc"></stop>
          <stop offset="100%" stop-color="#007adf"></stop>
        </linearGradient>
              <linearGradient id="SvgjsLinearGradient1029" x1="0%" y1="0%" x2="100%" y2="0%">
          <stop offset="0%" stop-color="#b88746"></stop>
          <stop offset="100%" stop-color="#fdf5a6"></stop>
        </linearGradient>
              <linearGradient id="SvgjsLinearGradient1028" x1="0%" y1="0%" x2="100%" y2="0%">
          <stop offset="0%" stop-color="#ffbf00"></stop>
          <stop offset="100%" stop-color="#26b5f8"></stop>
        </linearGradient>
          </defs>
  </svg>
    <rect x="0" y="0" width="100%" height="100%" fill="transparent" fill-opacity="1"></rect>
    <svg data-v-0dd9719b="" version="1.0" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="100%" height="100%" viewBox="0 0 100 100" preserveAspectRatio="xMidYMid meet" color-interpolation-filters="sRGB" style="margin: auto;"> <rect data-v-0dd9719b="" x="0" y="0" width="100%" height="100%" fill="#ffffff" fill-opacity="1" class="background" style="display: none;"></rect> <rect data-v-0dd9719b="" x="0" y="0" width="100%" height="100%" fill="url(#watermark)" fill-opacity="1" class="watermarklayer" style="display: none;"></rect> <g data-v-0dd9719b="" fill="#333" class="iconsvg-imagesvg"><rect fill="#333" fill-opacity="0" stroke-width="2" x="0" y="0" width="100" height="100" class="image-rect"></rect> <svg viewBox="0 0 60 60" x="0" y="0" width="100" height="100" class="image-svg-svg bn" style="overflow: visible;"><g hollow-target=""><g><svg fill="#000000" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xml:space="preserve" version="1.1" style="shape-rendering: geometricprecision; text-rendering: geometricprecision; overflow: visible;" viewBox="0 0 333 333" x="0" y="0" fill-rule="evenodd" clip-rule="evenodd" width="60" height="60" filtersec="colorsb9132488883" filter="url(#Hcww2fKrxx0prq2Q27whM)"><g transform="scale(1, 1) skewX(0)"><defs><filter id="SvgjsFilter1027"><feColorMatrix type="matrix" values="0 0 0 0 0.19921875  0 0 0 0 0.19921875  0 0 0 0 0.19921875  0 0 0 1 0"></feColorMatrix></filter></defs><g><path class="fil0" d="M79 0l175 0c22,0 42,9 56,23l0 0 0 0c14,15 23,34 23,56l0 175c0,22 -9,42 -23,56l0 0 0 0c-14,14 -34,23 -56,23l-175 0c-22,0 -41,-9 -56,-23l0 0 0 0c-14,-14 -23,-34 -23,-56l0 -175c0,-22 9,-41 23,-56l0 0 0 0c15,-14 34,-23 56,-23zm175 16l-175 0c-17,0 -33,7 -44,19l0 0c-12,11 -19,27 -19,44l0 175c0,18 7,33 19,45l0 0c11,11 27,18 44,18l175 0c18,0 33,-7 45,-18l0 0c11,-12 18,-27 18,-45l0 -175c0,-17 -7,-33 -18,-44l0 0c-12,-12 -27,-19 -45,-19z"></path></g></g></svg></g> <g filter="url(#colors4287342287)"><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 138.38400000000001 67.176" width="45" height="45" x="7.5" y="7.5" class="image-svg-letter"><path transform="translate(-2.8800000000000003 59.760000000000005)" d="M50.33-36.86L67.25-36.86L67.25-29.59L21.82-29.59L21.82-36.86L38.38-36.86L38.38-39.02L24.62-39.02L24.62-45.58L38.38-45.58L38.38-48.10L23.18-48.10L23.18-49.97L16.20-41.40Q14.18-43.99 10.91-47.45Q7.63-50.90 5.26-52.78L5.26-52.78L9.22-58.39Q12.38-57.31 16.38-55.40Q20.38-53.50 23.18-51.55L23.18-51.55L23.18-54.65L38.38-54.65L38.38-59.76L50.33-59.76L50.33-54.65L65.81-54.65L65.81-48.10L50.33-48.10L50.33-45.58L64.44-45.58L64.44-39.02L50.33-39.02L50.33-36.86ZM21.74-33.05L13.90-23.18Q11.74-25.85 8.50-29.27Q5.26-32.69 2.95-34.56L2.95-34.56L6.91-40.46Q10.30-39.38 14.62-37.22Q18.94-35.06 21.74-33.05L21.74-33.05ZM64.30-27.79L64.30-2.16Q64.30 2.74 62.64 5.08Q60.98 7.42 57.60 7.42L57.60 7.42L52.34 7.42L51.34 2.30L48.96 2.30L48.96-0.36L52.92-0.36L52.92-2.88L36.58-2.88L36.58 7.42L25.27 7.42L25.27-27.79L36.58-27.79L36.58-24.19Q37.15-25.92 38.56-26.86Q39.96-27.79 41.76-27.79L41.76-27.79L64.30-27.79ZM52.92-18.29L52.92-20.66L36.58-20.59L36.58-18.29L52.92-18.29ZM2.88 0.36Q10.44-10.15 14.33-20.30L14.33-20.30L22.10-17.64Q20.81-10.80 19.22-5.87Q17.64-0.94 14.04 6.48L14.04 6.48L2.88 0.36ZM36.58-11.74L36.58-9.43L52.92-9.43L52.92-11.74L36.58-11.74ZM108.14-37.87L99.50-37.87L99.50 7.42L87.77 7.42L87.77-35.57Q85.32-31.03 80.06-24.84L80.06-24.84L74.74-27.65Q77.54-36.43 79.16-43.34Q80.78-50.26 82.01-59.11L82.01-59.11L94.18-57.17Q93.24-51.62 92.23-47.66L92.23-47.66L108.14-47.66L108.14-37.87ZM141.26-47.66L141.26-37.87L133.78-37.87L133.78-1.80Q133.78 7.42 127.37 7.42L127.37 7.42L117.94 7.42L116.93-0.36L113.98-0.36L113.98-2.88L122.33-2.88L122.33-37.87L120.02-37.87Q118.58-34.70 116.50-31.72Q114.41-28.73 111.10-24.84L111.10-24.84L105.77-27.65Q108.58-36.43 110.20-43.34Q111.82-50.26 113.04-59.11L113.04-59.11L125.28-57.17Q124.27-51.62 123.26-47.66L123.26-47.66L141.26-47.66Z"></path></svg></g></g></svg> <defs><filter id="SvgjsFilter1026"><feColorMatrix type="matrix" values="0 0 0 0 0.19921875  0 0 0 0 0.19921875  0 0 0 0 0.19921875  0 0 0 1 0" class="icon-fecolormatrix"></feColorMatrix></filter> <filter id="SvgjsFilter1025"><feColorMatrix type="matrix" values="0 0 0 0 0.99609375  0 0 0 0 0.99609375  0 0 0 0 0.99609375  0 0 0 1 0" class="icon-fecolormatrix"></feColorMatrix></filter> <filter id="SvgjsFilter1024"><feColorMatrix type="matrix" values="0 0 0 0 0  0 0 0 0 0  0 0 0 0 0  0 0 0 1 0" class="icon-fecolormatrix"></feColorMatrix></filter></defs></g><defs v-gra="od"></defs></svg>
  </svg><style>@media (prefers-color-scheme: light) { :root { filter: none; } }
@media (prefers-color-scheme: dark) { :root { filter: none; } }
</style></svg>